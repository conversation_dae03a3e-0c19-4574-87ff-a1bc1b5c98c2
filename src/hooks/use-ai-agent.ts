import { useCallback, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { toast } from 'sonner'
import { useProjectStore } from '@/store/project.store'
import { useAuthStore } from '@/store/auth.store'
import { Message, Task } from '@/types/project'
import { saveProject } from '@/services/project.service'

export const useAIAgent = () => {
    const [isLoading, setIsLoading] = useState(false)
    const [isSaving, setIsSaving] = useState(false)

    const projectStore = useProjectStore()
    const {
        messages,
        tasks,
        projectData,
        projectSession,
        showTasksPanel,
        addMessage,
        updateMessage,
        setTasks,
        addTask,
        setProjectSession,
        setShowTasksPanel,
        updateProjectData,
        resetProject,
    } = projectStore

    const { getUserId, getActiveWorkspaceId, user } = useAuthStore()
    const selectedProject = projectStore.selectedProject ?? null
    const setSelectedProject = projectStore.setSelectedProject ?? (() => {})

    const parseSSE = async function* (response: Response) {
        const reader = response.body?.getReader()
        const decoder = new TextDecoder()
        let buffer = ''
        while (true) {
            const { done, value } = await reader!.read()
            if (done) break
            buffer += decoder.decode(value, { stream: true })
            const parts = buffer.split('\n')
            buffer = parts.pop()!
            for (const line of parts) {
                if (line.startsWith('data: ')) {
                    yield line.slice(6)
                }
            }
        }
    }

    const sendMessage = useCallback(
        async (message: string) => {
            if (!message.trim() || isLoading) return

            const sessionId = projectSession || uuidv4()
            if (!projectSession) setProjectSession(sessionId)

            const userMessage: Message = {
                id: uuidv4(),
                content: message,
                sender: 'user',
                timestamp: new Date(),
            }

            const aiMessageId = uuidv4()
            const aiMessage: Message = {
                id: aiMessageId,
                content: '',
                sender: 'ai',
                timestamp: new Date(),
            }
            addMessage(aiMessage)

            addMessage(userMessage)
            setIsLoading(true)

            try {
                const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat/ai`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Accept: 'text/event-stream',
                    },
                    body: JSON.stringify({ message, sessionId, selectedProjectId: selectedProject?.id }),
                })

                if (!res.ok || !res.body) throw new Error('Failed to stream response.')

                for await (const line of parseSSE(res)) {
                    try {
                        const parsed = JSON.parse(line)
                        if (parsed.content) {
                            updateMessage(aiMessageId, (prev) => prev + parsed.content)
                        }
                    } catch (err) {
                        console.warn('Stream parse error:', line, err)
                    }
                }

                updateProjectData()
            } catch (err) {
                console.error('Send message error:', err)
                updateMessage(aiMessageId, '⚠️ Something went wrong while streaming.')
            } finally {
                setIsLoading(false)
            }
        },
        [projectSession, isLoading, selectedProject, addMessage, addTask, setShowTasksPanel, setTasks, updateMessage]
    )

    const generateTasks = useCallback(async () => {
        if (!projectSession) {
            toast.error('Please start a conversation first.')
            return
        }

        setIsLoading(true)
        setTasks([])

        const taskPromptMessage: Message = {
            id: uuidv4(),
            content: 'Generate tasks for this project',
            sender: 'user',
            timestamp: new Date(),
        }

        const aiMessage: Message = {
            id: uuidv4(),
            content: 'Generating detailed project tasks...',
            sender: 'ai',
            timestamp: new Date(),
        }

        addMessage(taskPromptMessage)
        addMessage(aiMessage)

        try {
            const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat/ai`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'text/event-stream',
                },
                body: JSON.stringify({
                    message: 'generate_tasks',
                    sessionId: projectSession,
                    generateTasks: true,
                }),
            })

            const generatedTasks: Task[] = []

            for await (const line of parseSSE(res)) {
                const parsed = JSON.parse(line)
                if (parsed.task) {
                    generatedTasks.push(parsed.task)
                    addTask(parsed.task)
                    if (!showTasksPanel) setShowTasksPanel(true)
                } else if (parsed.message === 'Stream completed') {
                    updateMessage(aiMessage.id, `I've generated ${generatedTasks.length} tasks for your project!`)
                }
            }
        } catch (error) {
            console.error('Task generation error:', error)
            updateMessage(aiMessage.id, '⚠️ Something went wrong while generating tasks.')
        } finally {
            setIsLoading(false)
        }
    }, [
        projectSession,
        showTasksPanel,
        isLoading,
        selectedProject,
        addMessage,
        addTask,
        setShowTasksPanel,
        setTasks,
        updateMessage,
    ])

    const publishTasks = useCallback(async () => {
        const workspaceId = getActiveWorkspaceId()
        const userId = getUserId()

        console.log('messages', messages)

        if (!workspaceId) return toast.error('Select a workspace first.')
        if (!user || !userId) return toast.error('Log in to publish tasks.')
        if (!tasks.length) return toast.error('No tasks to publish.')

        const projectName = selectedProject?.name || projectData.name || 'Untitled'
        const existingProjectId = selectedProject?.id?.toString() || null

        setIsSaving(true)

        try {
            const result = await saveProject(
                userId,
                projectName,
                projectData.description,
                workspaceId,
                tasks,
                projectData.techStack,
                existingProjectId
            )

            if (result?.status === 'success') {
                const isNew = result.data?.project?.isNew ?? true
                toast.success(isNew ? `Project "${projectName}" created!` : `Tasks added to "${projectName}".`)

                setShowTasksPanel(false)
                setSelectedProject(null)

                addMessage({
                    id: uuidv4(),
                    content: isNew ? `Project "${projectName}" is now live!` : `Tasks added to project "${projectName}".`,
                    sender: 'ai',
                    timestamp: new Date(),
                })
            } else {
                throw new Error(result?.message || 'Save failed.')
            }
        } catch (err) {
            console.error('Save error:', err)
            toast.error('Error saving project.')
        } finally {
            setIsSaving(false)
        }
    }, [
        tasks,
        projectData,
        selectedProject,
        user,
        addMessage,
        getActiveWorkspaceId,
        getUserId,
        setSelectedProject,
        setShowTasksPanel,
    ])

    return {
        messages,
        tasks,
        isLoading,
        isSaving,
        showTasksPanel,
        selectedProject,
        sendMessage,
        generateTasks,
        publishTasks,
        resetProject,
        setSelectedProject,
        hasMessages: messages.length > 0,
    }
}
